@php
    use App\Models\Document;
    use Illuminate\Support\Facades\Auth;

    $team = Auth::user()->currentTeam;
    $creditsUsed = $team->credits_used ?? 0;
    $currentCredits = $team->current_credits ?? 50;
    $usagePercentage = $currentCredits > 0 ? ($creditsUsed / $currentCredits) * 100 : 0;
@endphp

<div class="bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900">Current Plan</h3>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Free
            </span>
        </div>
    </div>
    
    <div class="p-6">
        <!-- Credit Usage Bar -->
        <div class="mb-6">
            <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-700">Credits Used</span>
                <span class="text-sm font-medium text-gray-900">{{ $creditsUsed }}/{{ $currentCredits }}</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: {{ min($usagePercentage, 100) }}%"></div>
            </div>
            @if($usagePercentage >= 80)
                <p class="text-xs text-orange-600 mt-1">
                    @if($usagePercentage >= 100)
                        You've used all your credits!
                    @else
                        You're running low on credits.
                    @endif
                </p>
            @endif
        </div>

        <!-- Plan Features -->
        <div class="space-y-3 mb-6">
            <h4 class="text-sm font-semibold text-gray-900">Plan Features</h4>
            
            <div class="flex items-center text-sm text-gray-600">
                <svg class="w-4 h-4 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>{{ $currentCredits }} one-time credits</span>
            </div>
            
            <div class="flex items-center text-sm text-gray-600">
                <svg class="w-4 h-4 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>Some document types</span>
            </div>
            
            <div class="flex items-center text-sm text-gray-600">
                <svg class="w-4 h-4 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>1 team member</span>
            </div>
            
            <div class="flex items-center text-sm text-gray-600">
                <svg class="w-4 h-4 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>Basic OCR</span>
            </div>
            
            <div class="flex items-center text-sm text-gray-600">
                <svg class="w-4 h-4 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>Email support</span>
            </div>
        </div>

        <!-- Upgrade Button -->
        <button disabled class="w-full bg-gray-300 text-gray-500 px-4 py-2 rounded-lg cursor-not-allowed transition duration-200">
            <div class="flex items-center justify-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
                Upgrade Plan
            </div>
        </button>
        <p class="text-xs text-gray-500 text-center mt-2">Coming soon</p>
    </div>
</div>
