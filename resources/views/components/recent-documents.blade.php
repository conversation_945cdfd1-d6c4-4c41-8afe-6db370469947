@php
    use App\Models\Document;
    use Illuminate\Support\Facades\Auth;

    $teamId = Auth::user()->currentTeam->id;

    // Get recent documents
    $recentDocuments = Document::where('team_id', $teamId)
        ->with('documentType')
        ->orderBy('created_at', 'desc')
        ->limit(5)
        ->get();
@endphp

<div class="bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900">Recent Documents</h3>
            <a href="{{ route('documents') }}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                View All
            </a>
        </div>
    </div>
    
    <div class="p-6">
        @if($recentDocuments->count() > 0)
            <div class="space-y-4">
                @foreach($recentDocuments as $document)
                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:shadow-md transition duration-200">
                        <div class="flex items-center space-x-4">
                            @php
                                $docTypeColors = [
                                    'nic' => 'text-blue-600 bg-blue-50',
                                    'license' => 'text-green-600 bg-green-50',
                                    'vehicle' => 'text-purple-600 bg-purple-50',
                                    'deed' => 'text-orange-600 bg-orange-50',
                                    'invoice' => 'text-red-600 bg-red-50',
                                ];
                                $colorClass = $docTypeColors[$document->document_type] ?? 'text-gray-600 bg-gray-50';
                            @endphp
                            
                            <div class="w-10 h-10 {{ $colorClass }} rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            
                            <div>
                                <h4 class="font-medium text-gray-900">{{ $document->document_name }}</h4>
                                <div class="flex items-center space-x-4 text-sm text-gray-500">
                                    <span>{{ $document->documentType ? $document->documentType->name : $document->document_type }}</span>
                                    <span>•</span>
                                    <span>{{ $document->created_at->format('M j, Y') }}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-3">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                @if($document->status === 'completed') bg-green-100 text-green-800
                                @elseif($document->status === 'failed') bg-red-100 text-red-800
                                @elseif($document->status === 'processing') bg-yellow-100 text-yellow-800
                                @else bg-gray-100 text-gray-800 @endif">
                                {{ ucfirst($document->status) }}
                            </span>
                            
                            <a href="{{ route('document.view', $document->uuid) }}" 
                               class="text-blue-600 hover:text-blue-800">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="text-center py-8">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No documents yet</h3>
                <p class="mt-1 text-sm text-gray-500">Get started by uploading your first document.</p>
                <div class="mt-6">
                    <a href="{{ route('upload') }}" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition duration-200">
                        Upload Document
                    </a>
                </div>
            </div>
        @endif
    </div>
</div>
