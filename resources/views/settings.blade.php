<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Settings') }}
        </h2>
    </x-slot>

    <div class="py-8">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Settings Navigation -->
            <div class="bg-white shadow-sm rounded-lg mb-6">
                <div class="p-6">
                    <div class="flex flex-wrap gap-2 sm:gap-4">
                        <button class="flex items-center px-3 py-2 sm:px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition duration-200 text-sm sm:text-base" onclick="showSection('export')">
                            <svg class="w-4 h-4 mr-1 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <span class="hidden sm:inline">Export Settings</span>
                            <span class="sm:hidden">Export</span>
                        </button>
                        <button class="flex items-center px-3 py-2 sm:px-4 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition duration-200 text-sm sm:text-base" onclick="showSection('api')">
                            <svg class="w-4 h-4 mr-1 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <span class="hidden sm:inline">API Configuration</span>
                            <span class="sm:hidden">API</span>
                        </button>
                        <button class="flex items-center px-3 py-2 sm:px-4 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition duration-200 text-sm sm:text-base" onclick="showSection('delivery')">
                            <svg class="w-4 h-4 mr-1 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                            </svg>
                            <span class="hidden sm:inline">Delivery Options</span>
                            <span class="sm:hidden">Delivery</span>
                        </button>
                        <button class="flex items-center px-3 py-2 sm:px-4 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition duration-200 text-sm sm:text-base" onclick="showSection('mapping')">
                            <svg class="w-4 h-4 mr-1 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <span class="hidden sm:inline">Field Name Mapping</span>
                            <span class="sm:hidden">Mapping</span>
                        </button>
                        <button class="flex items-center px-3 py-2 sm:px-4 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition duration-200 text-sm sm:text-base" onclick="showSection('verification')">
                            <svg class="w-4 h-4 mr-1 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="hidden sm:inline">Verification Options</span>
                            <span class="sm:hidden">Verify</span>
                        </button>
                        <button class="flex items-center px-3 py-2 sm:px-4 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition duration-200 text-sm sm:text-base" onclick="showSection('roles')">
                            <svg class="w-4 h-4 mr-1 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                            <span class="hidden sm:inline">User Roles Management</span>
                            <span class="sm:hidden">Roles</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Export Settings Section -->
            <div id="export-section" class="settings-section bg-white shadow-sm rounded-lg mb-6">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Export Settings</h3>
                    <p class="text-sm text-gray-600 mt-1">Configure how your data is exported and formatted</p>
                </div>
                <div class="p-6 space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Default Export Format</label>
                            <select class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="csv">CSV</option>
                                <option value="json">JSON</option>
                                <option value="xml">XML</option>
                                <option value="excel">Excel</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Date Format</label>
                            <select class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="Y-m-d">YYYY-MM-DD</option>
                                <option value="d/m/Y">DD/MM/YYYY</option>
                                <option value="m/d/Y">MM/DD/YYYY</option>
                            </select>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="include-metadata" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        <label for="include-metadata" class="ml-2 text-sm text-gray-700">Include metadata in exports</label>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="auto-export" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        <label for="auto-export" class="ml-2 text-sm text-gray-700">Enable automatic exports</label>
                    </div>
                    <div class="pt-4">
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition duration-200">
                            Save Export Settings
                        </button>
                    </div>
                </div>
            </div>

            <!-- API Configuration Section -->
            <div id="api-section" class="settings-section bg-white shadow-sm rounded-lg mb-6 hidden">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">API Configuration</h3>
                    <p class="text-sm text-gray-600 mt-1">Manage API endpoints and authentication settings</p>
                </div>
                <div class="p-6 space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">API Base URL</label>
                        <input type="url" class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="https://api.smartocr.lk/v1">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Webhook URL</label>
                        <input type="url" class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="https://your-app.com/webhook">
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Rate Limit (requests/minute)</label>
                            <input type="number" class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500" value="60">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Timeout (seconds)</label>
                            <input type="number" class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500" value="30">
                        </div>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="enable-webhooks" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        <label for="enable-webhooks" class="ml-2 text-sm text-gray-700">Enable webhook notifications</label>
                    </div>
                    <div class="pt-4">
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition duration-200">
                            Save API Settings
                        </button>
                    </div>
                </div>
            </div>

            <!-- Delivery Options Section -->
            <div id="delivery-section" class="settings-section bg-white shadow-sm rounded-lg mb-6 hidden">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Delivery Options</h3>
                    <p class="text-sm text-gray-600 mt-1">Configure how processed documents are delivered</p>
                </div>
                <div class="p-6 space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-3">Delivery Method</label>
                        <div class="space-y-2">
                            <div class="flex items-center">
                                <input type="radio" id="delivery-email" name="delivery-method" value="email" class="text-blue-600 focus:ring-blue-500">
                                <label for="delivery-email" class="ml-2 text-sm text-gray-700">Email notification</label>
                            </div>
                            <div class="flex items-center">
                                <input type="radio" id="delivery-webhook" name="delivery-method" value="webhook" class="text-blue-600 focus:ring-blue-500">
                                <label for="delivery-webhook" class="ml-2 text-sm text-gray-700">Webhook callback</label>
                            </div>
                            <div class="flex items-center">
                                <input type="radio" id="delivery-ftp" name="delivery-method" value="ftp" class="text-blue-600 focus:ring-blue-500">
                                <label for="delivery-ftp" class="ml-2 text-sm text-gray-700">FTP upload</label>
                            </div>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Email Recipients</label>
                        <textarea class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500" rows="3" placeholder="Enter email addresses, one per line"></textarea>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">FTP Server</label>
                            <input type="text" class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="ftp.example.com">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">FTP Path</label>
                            <input type="text" class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="/uploads/">
                        </div>
                    </div>
                    <div class="pt-4">
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition duration-200">
                            Save Delivery Settings
                        </button>
                    </div>
                </div>
            </div>

            <!-- Field Name Mapping Section -->
            <div id="mapping-section" class="settings-section bg-white shadow-sm rounded-lg mb-6 hidden">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Field Name Mapping</h3>
                    <p class="text-sm text-gray-600 mt-1">Customize field names for different document types</p>
                </div>
                <div class="p-6 space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Document Type</label>
                        <select class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="nic">National Identity Card</option>
                            <option value="license">Driving License</option>
                            <option value="vehicle">Vehicle Registration</option>
                            <option value="deed">Land Deed</option>
                            <option value="invoice">Invoice</option>
                        </select>
                    </div>
                    <div class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">System Field</label>
                                <input type="text" class="w-full border-gray-300 rounded-md shadow-sm bg-gray-100" value="full_name" readonly>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Custom Field Name</label>
                                <input type="text" class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="Full Name">
                            </div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">System Field</label>
                                <input type="text" class="w-full border-gray-300 rounded-md shadow-sm bg-gray-100" value="id_number" readonly>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Custom Field Name</label>
                                <input type="text" class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="ID Number">
                            </div>
                        </div>
                    </div>
                    <div class="pt-4">
                        <button class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition duration-200 mr-2">
                            Add Field Mapping
                        </button>
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition duration-200">
                            Save Mappings
                        </button>
                    </div>
                </div>
            </div>

            <!-- Verification Options Section -->
            <div id="verification-section" class="settings-section bg-white shadow-sm rounded-lg mb-6 hidden">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Verification Options</h3>
                    <p class="text-sm text-gray-600 mt-1">Configure data verification and validation settings</p>
                </div>
                <div class="p-6 space-y-6">
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox" id="enable-verification" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <label for="enable-verification" class="ml-2 text-sm text-gray-700">Enable automatic data verification</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="manual-review" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <label for="manual-review" class="ml-2 text-sm text-gray-700">Require manual review for low confidence results</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="duplicate-check" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <label for="duplicate-check" class="ml-2 text-sm text-gray-700">Check for duplicate documents</label>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Confidence Threshold (%)</label>
                            <input type="number" class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500" value="85" min="0" max="100">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Verification Timeout (seconds)</label>
                            <input type="number" class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500" value="30">
                        </div>
                    </div>
                    <div class="pt-4">
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition duration-200">
                            Save Verification Settings
                        </button>
                    </div>
                </div>
            </div>

            <!-- User Roles Management Section -->
            <div id="roles-section" class="settings-section bg-white shadow-sm rounded-lg mb-6 hidden">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">User Roles Management</h3>
                    <p class="text-sm text-gray-600 mt-1">Manage user permissions and access levels</p>
                </div>
                <div class="p-6 space-y-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Upload</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">View</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Export</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Settings</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Admin</td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" checked disabled class="rounded border-gray-300 text-blue-600"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" checked disabled class="rounded border-gray-300 text-blue-600"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" checked disabled class="rounded border-gray-300 text-blue-600"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" checked disabled class="rounded border-gray-300 text-blue-600"></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Default role</td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Editor</td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" checked class="rounded border-gray-300 text-blue-600"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" checked class="rounded border-gray-300 text-blue-600"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" checked class="rounded border-gray-300 text-blue-600"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" class="rounded border-gray-300 text-blue-600"></td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <button class="text-blue-600 hover:text-blue-900 text-sm">Edit</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Viewer</td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" class="rounded border-gray-300 text-blue-600"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" checked class="rounded border-gray-300 text-blue-600"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" class="rounded border-gray-300 text-blue-600"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" class="rounded border-gray-300 text-blue-600"></td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <button class="text-blue-600 hover:text-blue-900 text-sm">Edit</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="pt-4">
                        <button class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition duration-200 mr-2">
                            Add New Role
                        </button>
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition duration-200">
                            Save Role Settings
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showSection(sectionName) {
            // Hide all sections
            document.querySelectorAll('.settings-section').forEach(section => {
                section.classList.add('hidden');
            });
            
            // Reset all button styles
            document.querySelectorAll('button[onclick^="showSection"]').forEach(button => {
                button.classList.remove('bg-blue-600', 'text-white');
                button.classList.add('bg-gray-200', 'text-gray-700');
            });
            
            // Show selected section
            document.getElementById(sectionName + '-section').classList.remove('hidden');
            
            // Highlight active button
            event.target.classList.remove('bg-gray-200', 'text-gray-700');
            event.target.classList.add('bg-blue-600', 'text-white');
        }
    </script>
</x-app-layout>
