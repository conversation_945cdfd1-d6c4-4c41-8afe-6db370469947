<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Settings') }}
        </h2>
    </x-slot>

    <div class="py-8">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <!-- Settings Introduction -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-lg font-medium text-blue-900">Settings Preview</h3>
                        <p class="text-blue-800 text-sm mt-1">These settings show available features for SmartOCR. Contact us to enable specific features for your account.</p>
                    </div>
                </div>
            </div>

            <!-- Accordion Settings -->
            <div class="bg-white shadow-sm rounded-lg">
                <!-- Export Settings Accordion -->
                <div class="border-b border-gray-200">
                    <button class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 focus:outline-none focus:bg-gray-50" onclick="toggleAccordion('export')">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <span class="text-lg font-medium text-gray-900">Export Settings</span>
                        </div>
                        <svg class="w-5 h-5 text-gray-400 transform transition-transform duration-200" id="export-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="hidden px-6 pb-4" id="export-content">
                        <p class="text-sm text-gray-600 mb-4">Configure how your data is exported and formatted</p>
                        <div class="space-y-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Default Export Format</label>
                                    <select class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500" disabled>
                                        <option value="csv">CSV</option>
                                        <option value="json">JSON</option>
                                        <option value="xml">XML</option>
                                        <option value="excel">Excel</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Date Format</label>
                                    <select class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500" disabled>
                                        <option value="Y-m-d">YYYY-MM-DD</option>
                                        <option value="d/m/Y">DD/MM/YYYY</option>
                                        <option value="m/d/Y">MM/DD/YYYY</option>
                                    </select>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="include-metadata" class="rounded border-gray-300 text-blue-600 shadow-sm" disabled>
                                <label for="include-metadata" class="ml-2 text-sm text-gray-700">Include metadata in exports</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="auto-export" class="rounded border-gray-300 text-blue-600 shadow-sm" disabled>
                                <label for="auto-export" class="ml-2 text-sm text-gray-700">Enable automatic exports</label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- API Configuration Accordion -->
                <div class="border-b border-gray-200">
                    <button class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 focus:outline-none focus:bg-gray-50" onclick="toggleAccordion('api')">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <span class="text-lg font-medium text-gray-900">API Configuration</span>
                        </div>
                        <svg class="w-5 h-5 text-gray-400 transform transition-transform duration-200" id="api-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="hidden px-6 pb-4" id="api-content">
                        <p class="text-sm text-gray-600 mb-4">Manage API endpoints and authentication settings</p>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">API Base URL</label>
                                <input type="url" class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="https://api.smartocr.lk/v1" disabled>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Webhook URL</label>
                                <input type="url" class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="https://your-app.com/webhook" disabled>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Rate Limit (requests/minute)</label>
                                    <input type="number" class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500" value="60" disabled>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Timeout (seconds)</label>
                                    <input type="number" class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500" value="30" disabled>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="enable-webhooks" class="rounded border-gray-300 text-blue-600 shadow-sm" disabled>
                                <label for="enable-webhooks" class="ml-2 text-sm text-gray-700">Enable webhook notifications</label>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Field Name Mapping Accordion -->
                <div class="border-b border-gray-200">
                    <button class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 focus:outline-none focus:bg-gray-50" onclick="toggleAccordion('mapping')">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-orange-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <span class="text-lg font-medium text-gray-900">Field Name Mapping</span>
                        </div>
                        <svg class="w-5 h-5 text-gray-400 transform transition-transform duration-200" id="mapping-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="hidden px-6 pb-4" id="mapping-content">
                        <p class="text-sm text-gray-600 mb-4">Customize field names for different document types</p>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Document Type</label>
                                <select class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500" disabled>
                                    <option value="nic">National Identity Card</option>
                                    <option value="license">Driving License</option>
                                    <option value="vehicle">Vehicle Registration</option>
                                    <option value="deed">Land Deed</option>
                                    <option value="invoice">Invoice</option>
                                </select>
                            </div>
                            <div class="space-y-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">System Field</label>
                                        <input type="text" class="w-full border-gray-300 rounded-md shadow-sm bg-gray-100" value="full_name" readonly disabled>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Custom Field Name</label>
                                        <input type="text" class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="Full Name" disabled>
                                    </div>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">System Field</label>
                                        <input type="text" class="w-full border-gray-300 rounded-md shadow-sm bg-gray-100" value="id_number" readonly disabled>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Custom Field Name</label>
                                        <input type="text" class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="ID Number" disabled>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Verification Options Accordion -->
                <div class="border-b border-gray-200">
                    <button class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 focus:outline-none focus:bg-gray-50" onclick="toggleAccordion('verification')">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-red-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-lg font-medium text-gray-900">Verification Options</span>
                        </div>
                        <svg class="w-5 h-5 text-gray-400 transform transition-transform duration-200" id="verification-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="hidden px-6 pb-4" id="verification-content">
                        <p class="text-sm text-gray-600 mb-4">Configure data verification and validation settings</p>
                        <div class="space-y-4">
                            <div class="space-y-3">
                                <div class="flex items-center">
                                    <input type="checkbox" id="enable-verification" class="rounded border-gray-300 text-blue-600 shadow-sm" disabled>
                                    <label for="enable-verification" class="ml-2 text-sm text-gray-700">Enable automatic data verification</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="manual-review" class="rounded border-gray-300 text-blue-600 shadow-sm" disabled>
                                    <label for="manual-review" class="ml-2 text-sm text-gray-700">Require manual review for low confidence results</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="duplicate-check" class="rounded border-gray-300 text-blue-600 shadow-sm" disabled>
                                    <label for="duplicate-check" class="ml-2 text-sm text-gray-700">Check for duplicate documents</label>
                                </div>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Confidence Threshold (%)</label>
                                    <input type="number" class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500" value="85" min="0" max="100" disabled>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Verification Timeout (seconds)</label>
                                    <input type="number" class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500" value="30" disabled>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- User Roles Management Accordion -->
                <div class="border-b border-gray-200">
                    <button class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 focus:outline-none focus:bg-gray-50" onclick="toggleAccordion('roles')">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-indigo-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                            <span class="text-lg font-medium text-gray-900">User Roles Management</span>
                        </div>
                        <svg class="w-5 h-5 text-gray-400 transform transition-transform duration-200" id="roles-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="hidden px-6 pb-4" id="roles-content">
                        <p class="text-sm text-gray-600 mb-4">Manage user permissions and access levels</p>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Upload</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">View</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Export</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Settings</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Admin</td>
                                        <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" checked disabled class="rounded border-gray-300 text-blue-600"></td>
                                        <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" checked disabled class="rounded border-gray-300 text-blue-600"></td>
                                        <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" checked disabled class="rounded border-gray-300 text-blue-600"></td>
                                        <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" checked disabled class="rounded border-gray-300 text-blue-600"></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Default role</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Editor</td>
                                        <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" checked disabled class="rounded border-gray-300 text-blue-600"></td>
                                        <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" checked disabled class="rounded border-gray-300 text-blue-600"></td>
                                        <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" checked disabled class="rounded border-gray-300 text-blue-600"></td>
                                        <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" disabled class="rounded border-gray-300 text-blue-600"></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Preview only</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Viewer</td>
                                        <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" disabled class="rounded border-gray-300 text-blue-600"></td>
                                        <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" checked disabled class="rounded border-gray-300 text-blue-600"></td>
                                        <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" disabled class="rounded border-gray-300 text-blue-600"></td>
                                        <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" disabled class="rounded border-gray-300 text-blue-600"></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Preview only</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Request Custom Document Types Accordion -->
                <div class="border-b border-gray-200">
                    <button class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 focus:outline-none focus:bg-gray-50" onclick="toggleAccordion('custom')">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-purple-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            <span class="text-lg font-medium text-gray-900">Request Custom Document Types</span>
                        </div>
                        <svg class="w-5 h-5 text-gray-400 transform transition-transform duration-200" id="custom-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="hidden px-6 pb-4" id="custom-content">
                        <p class="text-sm text-gray-600 mb-4">Request support for new document types by providing samples</p>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Document Type Name</label>
                                <input type="text" class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="e.g., Birth Certificate, Passport">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                                <textarea class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500" rows="3" placeholder="Describe the document type and what fields need to be extracted"></textarea>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Document Samples (Minimum 10 required)</label>
                                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                    <div class="mt-4">
                                        <label for="file-upload" class="cursor-pointer">
                                            <span class="mt-2 block text-sm font-medium text-gray-900">Upload document samples</span>
                                            <span class="mt-1 block text-xs text-gray-500">PNG, JPG, PDF up to 10MB each</span>
                                        </label>
                                        <input id="file-upload" name="file-upload" type="file" class="sr-only" multiple accept=".png,.jpg,.jpeg,.pdf">
                                    </div>
                                </div>
                            </div>
                            <div class="pt-4">
                                <button class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition duration-200">
                                    Submit Request
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <script>
        function toggleAccordion(sectionName) {
            const content = document.getElementById(sectionName + '-content');
            const icon = document.getElementById(sectionName + '-icon');

            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                icon.style.transform = 'rotate(180deg)';
            } else {
                content.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';
            }
        }
    </script>
</x-app-layout>
