<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Document Details
        </h2>
    </x-slot>

    <div class="py-8">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Page Header -->
            <div class="bg-white shadow-sm rounded-lg mb-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <h1 class="text-2xl font-bold text-gray-900">Document Details</h1>
                            <p class="text-sm text-gray-600">View extracted data and download results</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                <div class="p-6 lg:p-8">
                    <!-- Header -->
                    <div class="flex flex-col lg:flex-row lg:justify-between lg:items-start mb-8 space-y-4 lg:space-y-0">
                        <div class="flex-1">
                            <h1 class="text-2xl font-bold text-gray-900 mb-2">{{ $document->document_name }}</h1>
                            <div class="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-2 sm:space-y-0">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium w-fit
                                    @if($document->status === 'completed') bg-green-100 text-green-800
                                    @elseif($document->status === 'failed') bg-red-100 text-red-800
                                    @elseif($document->status === 'processing') bg-yellow-100 text-yellow-800
                                    @else bg-gray-100 text-gray-800 @endif">
                                    {{ ucfirst($document->status) }}
                                </span>
                                <span class="text-sm text-gray-500">
                                    {{ $document->documentType ? $document->documentType->name : $document->document_type }}
                                </span>
                            </div>
                        </div>
                        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
                            <a href="{{ route('documents') }}" class="inline-flex items-center justify-center bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition duration-200 text-sm">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                                </svg>
                                Back to Documents
                            </a>
                            @if($document->isCompleted())
                                <a href="{{ route('document.download.csv', $document) }}" class="inline-flex items-center justify-center bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition duration-200 text-sm">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    Download CSV
                                </a>
                            @endif
                        </div>
                    </div>

                    <!-- Document Information -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        <!-- Document Details -->
                        <div class="lg:col-span-1">
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">Document Information</h3>
                                <dl class="space-y-3">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Document ID</dt>
                                        <dd class="text-sm text-gray-900 font-mono">{{ $document->uuid }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Type</dt>
                                        <dd class="text-sm text-gray-900">{{ $document->documentType ? $document->documentType->name : $document->document_type }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Uploaded</dt>
                                        <dd class="text-sm text-gray-900">{{ $document->created_at->format('M j, Y g:i A') }}</dd>
                                    </div>
                                    @if($document->processed_at)
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Processed</dt>
                                            <dd class="text-sm text-gray-900">{{ $document->processed_at->format('M j, Y g:i A') }}</dd>
                                        </div>
                                    @endif
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Credits Used</dt>
                                        <dd class="text-sm text-gray-900">{{ $document->credit_used }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Request Type</dt>
                                        <dd class="text-sm text-gray-900">{{ ucfirst(str_replace('_', ' ', $document->request_type)) }}</dd>
                                    </div>
                                </dl>
                            </div>
                        </div>

                        <!-- Extracted Data -->
                        <div class="lg:col-span-2">
                            <div class="bg-white border border-gray-200 rounded-lg p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">Extracted Data</h3>
                                
                                @if($document->isCompleted() && $document->extracted_data)
                                    @php
                                        // Extract the actual data fields (skip 'type' and get 'data' content)
                                        $actualData = $document->extracted_data['data'] ?? $document->extracted_data;
                                    @endphp

                                    @if(is_array($actualData) && !empty($actualData))
                                        <div class="overflow-x-auto">
                                            <table class="min-w-full divide-y divide-gray-200">
                                                <thead class="bg-gray-50">
                                                    <tr>
                                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Field</th>
                                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                                                    </tr>
                                                </thead>
                                                <tbody class="bg-white divide-y divide-gray-200">
                                                    @foreach($actualData as $key => $value)
                                                        <tr class="hover:bg-gray-50">
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                                {{ ucfirst(str_replace('_', ' ', $key)) }}
                                                            </td>
                                                            <td class="px-6 py-4 text-sm text-gray-900">
                                                                @if(is_array($value))
                                                                    <div class="max-w-xs">
                                                                        <details class="cursor-pointer">
                                                                            <summary class="text-blue-600 hover:text-blue-800">View Array Data</summary>
                                                                            <pre class="mt-2 bg-gray-50 p-3 rounded text-xs overflow-x-auto">{{ json_encode($value, JSON_PRETTY_PRINT) }}</pre>
                                                                        </details>
                                                                    </div>
                                                                @else
                                                                    <div class="break-words">{{ $value ?: 'N/A' }}</div>
                                                                @endif
                                                            </td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    @else
                                        <div class="bg-gray-50 p-4 rounded">
                                            <p class="text-sm text-gray-600">No extracted data available to display.</p>
                                        </div>
                                    @endif
                                @elseif($document->isFailed())
                                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                                        <div class="flex">
                                            <div class="flex-shrink-0">
                                                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                            <div class="ml-3">
                                                <h3 class="text-sm font-medium text-red-800">Processing Failed</h3>
                                                <div class="mt-2 text-sm text-red-700">
                                                    @if(isset($document->extracted_data['error']))
                                                        {{ $document->extracted_data['error'] }}
                                                    @else
                                                        An error occurred while processing this document.
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @elseif($document->isProcessing())
                                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                        <div class="flex">
                                            <div class="flex-shrink-0">
                                                <svg class="animate-spin h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                </svg>
                                            </div>
                                            <div class="ml-3">
                                                <h3 class="text-sm font-medium text-yellow-800">Processing in Progress</h3>
                                                <div class="mt-2 text-sm text-yellow-700">
                                                    Your document is currently being processed. This page will automatically update when processing is complete.
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @else
                                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                                        <div class="text-center">
                                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                            </svg>
                                            <h3 class="mt-2 text-sm font-medium text-gray-900">No data available</h3>
                                            <p class="mt-1 text-sm text-gray-500">Document processing has not started yet.</p>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if($document->isProcessing())
        <script>
            // Auto-refresh page every 5 seconds if document is still processing
            setTimeout(function() {
                window.location.reload();
            }, 5000);
        </script>
    @endif
</x-app-layout>
