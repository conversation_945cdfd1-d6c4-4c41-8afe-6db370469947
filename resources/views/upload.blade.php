<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Upload Document') }}
        </h2>
    </x-slot>

    <div class="py-8">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">

            <!-- Main Content -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                <div class="p-6 lg:p-8">
                    
                    <!-- Document Type Selection Panel -->
                    <div class="bg-gray-50 rounded-lg p-6 mb-8">
                        <div class="flex items-center mb-4">
                            <svg class="h-5 w-5 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <h2 class="text-lg font-semibold text-gray-900">Document Type</h2>
                        </div>
                        <div class="max-w-lg">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Select the type of document you want to process</label>
                            <select id="documentType" class="w-full border-gray-300 rounded-lg shadow-sm focus:border-blue-500 focus:ring-blue-500 text-base py-3 px-4">
                                <option value="">Choose a document type...</option>
                                @foreach($documentTypes as $docType)
                                    <option value="{{ $docType->type }}" 
                                            data-enabled="{{ $docType->isEnabled() ? 'true' : 'false' }}"
                                            @if(!$docType->isEnabled()) disabled @endif>
                                        {{ $docType->name }}
                                        @if(!$docType->isEnabled()) (Coming Soon) @endif
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <!-- Document Type Info Panel -->
                    <div id="documentInfo" class="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4 hidden">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-blue-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="ml-3 flex-1">
                                <h3 id="docTypeName" class="text-base font-semibold text-blue-900 mb-1"></h3>
                                <p id="docTypeDescription" class="text-blue-800 text-sm mb-3"></p>
                                <div class="grid grid-cols-2 sm:grid-cols-4 gap-2 text-xs">
                                    <div class="bg-white rounded-md p-2">
                                        <div class="font-medium text-blue-900 text-xs">Credit Cost</div>
                                        <div id="docTypeCost" class="text-blue-800 text-sm font-semibold"></div>
                                    </div>
                                    <div class="bg-white rounded-md p-2">
                                        <div class="font-medium text-blue-900 text-xs">Files Required</div>
                                        <div id="docTypeFileCount" class="text-blue-800 text-sm font-semibold"></div>
                                    </div>
                                    <div class="bg-white rounded-md p-2">
                                        <div class="font-medium text-blue-900 text-xs">Max File Size</div>
                                        <div id="docTypeMaxSize" class="text-blue-800 text-sm font-semibold"></div>
                                    </div>
                                    <div class="bg-white rounded-md p-2">
                                        <div class="font-medium text-blue-900 text-xs">Formats</div>
                                        <div id="docTypeFormats" class="text-blue-800 text-xs"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- File Upload Section -->
                    <div id="uploadSection" class="bg-gray-50 rounded-lg p-6 hidden">
                        <div class="flex items-center mb-6">
                            <svg class="h-5 w-5 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            <h2 class="text-lg font-semibold text-gray-900">Upload Files</h2>
                        </div>
                        
                        <form id="uploadForm" enctype="multipart/form-data">
                            @csrf
                            <input type="hidden" id="selectedDocumentType" name="document_type" value="">
                            
                            <div class="space-y-6">
                                <!-- Primary File Upload -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        Primary Document
                                        <span id="file1Label" class="text-gray-500"></span>
                                    </label>
                                    <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg hover:border-gray-400 transition duration-200">
                                        <div class="space-y-1 text-center">
                                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                            </svg>
                                            <div class="flex text-sm text-gray-600">
                                                <label for="file1" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                                    <span>Upload a file</span>
                                                    <input id="file1" name="file1" type="file" class="sr-only">
                                                </label>
                                                <p class="pl-1">or drag and drop</p>
                                            </div>
                                            <p id="file1Info" class="text-xs text-gray-500"></p>
                                        </div>
                                    </div>
                                    <div id="file1Error" class="mt-2 text-sm text-red-600 hidden"></div>
                                    <div id="file1Success" class="mt-2 text-sm text-green-600 hidden"></div>
                                </div>

                                <!-- Secondary File Upload -->
                                <div id="file2Container" class="hidden">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        Secondary Document <span class="text-gray-500">(Back side)</span>
                                    </label>
                                    <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg hover:border-gray-400 transition duration-200">
                                        <div class="space-y-1 text-center">
                                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                            </svg>
                                            <div class="flex text-sm text-gray-600">
                                                <label for="file2" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                                    <span>Upload a file</span>
                                                    <input id="file2" name="file2" type="file" class="sr-only">
                                                </label>
                                                <p class="pl-1">or drag and drop</p>
                                            </div>
                                            <p id="file2Info" class="text-xs text-gray-500"></p>
                                        </div>
                                    </div>
                                    <div id="file2Error" class="mt-2 text-sm text-red-600 hidden"></div>
                                    <div id="file2Success" class="mt-2 text-sm text-green-600 hidden"></div>
                                </div>

                                <!-- Process Button -->
                                <div class="flex justify-end">
                                    <button type="submit" id="processBtn" class="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition duration-200 relative" disabled>
                                        <span id="processText" class="flex items-center">
                                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            Process Document
                                        </span>
                                        <span id="processLoading" class="hidden flex items-center">
                                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                            Processing...
                                        </span>
                                    </button>
                                </div>

                                <!-- Progress Bar -->
                                <div id="progressContainer" class="hidden">
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div id="progressBar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                                    </div>
                                    <p id="progressText" class="text-sm text-gray-600 mt-2 text-center"></p>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const documentTypeSelect = document.getElementById('documentType');
            const documentInfo = document.getElementById('documentInfo');
            const uploadSection = document.getElementById('uploadSection');
            const uploadForm = document.getElementById('uploadForm');
            const processBtn = document.getElementById('processBtn');
            const file1Input = document.getElementById('file1');
            const file2Input = document.getElementById('file2');
            const file2Container = document.getElementById('file2Container');

            let currentDocumentType = null;
            let file1Valid = false;
            let file2Valid = false;
            let file2Required = false;

            // Document type selection handler
            documentTypeSelect.addEventListener('change', function() {
                const selectedType = this.value;

                if (!selectedType) {
                    documentInfo.classList.add('hidden');
                    uploadSection.classList.add('hidden');
                    return;
                }

                // Fetch document type details
                fetch(`{{ route('upload.get-type') }}?type=${selectedType}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.error) {
                            alert('Error loading document type details');
                            return;
                        }

                        currentDocumentType = data;
                        updateDocumentInfo(data);
                        setupFileInputs(data);
                        documentInfo.classList.remove('hidden');
                        uploadSection.classList.remove('hidden');

                        document.getElementById('selectedDocumentType').value = selectedType;
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Error loading document type details');
                    });
            });

            function updateDocumentInfo(data) {
                document.getElementById('docTypeName').textContent = data.name;
                document.getElementById('docTypeDescription').textContent = data.description;
                document.getElementById('docTypeCost').textContent = data.credit_cost;
                document.getElementById('docTypeFileCount').textContent = data.file_count;
                document.getElementById('docTypeMaxSize').textContent = data.max_file_size_mb + 'MB';
                document.getElementById('docTypeFormats').textContent = data.allowed_extensions.map(ext => ext.toUpperCase()).join(', ');
            }

            function setupFileInputs(data) {
                // Setup file1
                const acceptedTypes = data.allowed_extensions.map(ext => '.' + ext).join(',');
                file1Input.setAttribute('accept', acceptedTypes);
                file1Input.setAttribute('data-max-size', data.max_file_size_mb * 1024 * 1024);

                document.getElementById('file1Info').textContent =
                    `${data.allowed_extensions.map(ext => ext.toUpperCase()).join(', ')} up to ${data.max_file_size_mb}MB`;

                // Setup file2 if needed
                file2Required = data.file_count > 1;
                if (file2Required) {
                    file2Container.classList.remove('hidden');
                    file2Input.setAttribute('accept', acceptedTypes);
                    file2Input.setAttribute('data-max-size', data.max_file_size_mb * 1024 * 1024);
                    document.getElementById('file2Info').textContent =
                        `${data.allowed_extensions.map(ext => ext.toUpperCase()).join(', ')} up to ${data.max_file_size_mb}MB`;
                    document.getElementById('file1Label').textContent = '(Front side or main document)';
                } else {
                    file2Container.classList.add('hidden');
                    document.getElementById('file1Label').textContent = '';
                }

                // Reset validation states
                file1Valid = false;
                file2Valid = !file2Required;
                updateProcessButton();
            }

            // File validation function
            function validateFile(input, isFile2 = false) {
                const file = input.files[0];
                const errorElement = document.getElementById(isFile2 ? 'file2Error' : 'file1Error');
                const successElement = document.getElementById(isFile2 ? 'file2Success' : 'file1Success');

                // Clear previous states
                errorElement.classList.add('hidden');
                successElement.classList.add('hidden');

                if (!file) {
                    if (isFile2) file2Valid = !file2Required;
                    else file1Valid = false;
                    updateProcessButton();
                    return;
                }

                if (!currentDocumentType) return;

                // Check file size
                const maxSize = parseInt(input.getAttribute('data-max-size'));
                if (file.size > maxSize) {
                    const maxSizeMB = Math.round(maxSize / (1024 * 1024));
                    errorElement.textContent = `File too large. Maximum size is ${maxSizeMB}MB.`;
                    errorElement.classList.remove('hidden');
                    if (isFile2) file2Valid = false;
                    else file1Valid = false;
                    updateProcessButton();
                    return;
                }

                // Check file extension
                const fileName = file.name.toLowerCase();
                const fileExtension = fileName.split('.').pop();
                const allowedExtensions = currentDocumentType.allowed_extensions.map(ext => ext.toLowerCase());

                if (!allowedExtensions.includes(fileExtension)) {
                    errorElement.textContent = `Invalid file format. Allowed formats: ${allowedExtensions.map(ext => ext.toUpperCase()).join(', ')}`;
                    errorElement.classList.remove('hidden');
                    if (isFile2) file2Valid = false;
                    else file1Valid = false;
                    updateProcessButton();
                    return;
                }

                // File is valid
                successElement.textContent = `✓ ${file.name} (${(file.size / 1024).toFixed(1)} KB)`;
                successElement.classList.remove('hidden');
                if (isFile2) file2Valid = true;
                else file1Valid = true;
                updateProcessButton();
            }

            function updateProcessButton() {
                const isValid = file1Valid && file2Valid;
                processBtn.disabled = !isValid;
            }

            // File input event listeners
            file1Input.addEventListener('change', () => validateFile(file1Input, false));
            file2Input.addEventListener('change', () => validateFile(file2Input, true));

            // Form submission
            uploadForm.addEventListener('submit', function(e) {
                e.preventDefault();

                if (!file1Valid || !file2Valid) {
                    alert('Please fix file validation errors before proceeding.');
                    return;
                }

                processDocument();
            });

            function processDocument() {
                const formData = new FormData(uploadForm);

                // Show loading state
                document.getElementById('processText').classList.add('hidden');
                document.getElementById('processLoading').classList.remove('hidden');
                document.getElementById('progressContainer').classList.remove('hidden');
                processBtn.disabled = true;

                // Simulate progress updates
                let progress = 0;
                const progressInterval = setInterval(() => {
                    progress += Math.random() * 20;
                    if (progress > 90) progress = 90;
                    document.getElementById('progressBar').style.width = progress + '%';
                    document.getElementById('progressText').textContent = `Processing... ${Math.round(progress)}%`;
                }, 500);

                fetch('{{ route('upload.process') }}', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    clearInterval(progressInterval);
                    document.getElementById('progressBar').style.width = '100%';
                    document.getElementById('progressText').textContent = 'Complete!';

                    if (data.success) {
                        setTimeout(() => {
                            window.location.href = data.redirect_url;
                        }, 1000);
                    } else {
                        alert('Error: ' + data.message);
                        resetForm();
                    }
                })
                .catch(error => {
                    clearInterval(progressInterval);
                    console.error('Error:', error);
                    alert('An error occurred while processing the document.');
                    resetForm();
                });
            }

            function resetForm() {
                document.getElementById('processText').classList.remove('hidden');
                document.getElementById('processLoading').classList.add('hidden');
                document.getElementById('progressContainer').classList.add('hidden');
                processBtn.disabled = false;
                document.getElementById('progressBar').style.width = '0%';
            }
        });
    </script>
</x-app-layout>
