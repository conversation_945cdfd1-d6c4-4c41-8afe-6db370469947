<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Services\OcrService;

class OcrServiceTest extends TestCase
{
    protected $ocrService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->ocrService = new OcrService();
    }

    /**
     * Test OCR service with logo image using type=id
     */
    public function test_ocr_service_with_logo_image()
    {
        // Check if logo.png exists
        $logoPath = public_path('logo.png');

        if (!file_exists($logoPath)) {
            $this->markTestSkipped('Logo file not found at: ' . $logoPath);
        }

        // Test the OCR service with type=id
        $result = $this->ocrService->processOcr('id', $logoPath);

        // Assert the response structure
        $this->assertIsArray($result);
        $this->assertArrayHasKey('type', $result);

        // The API should return either success or error
        $this->assertContains($result['type'], ['success', 'error']);

        if ($result['type'] === 'success') {
            $this->assertArrayHasKey('data', $result);
            echo "\nOCR Success! Extracted data: " . json_encode($result['data'], JSON_PRETTY_PRINT);
        } else {
            $this->assertArrayHasKey('reason', $result);
            echo "\nOCR Error: " . $result['reason'];
        }

        // Test passes regardless of OCR result since we're testing the service integration
        $this->assertTrue(true, 'OCR service integration test completed');
    }
}
