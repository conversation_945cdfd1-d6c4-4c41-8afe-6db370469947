<?php

use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

Route::get('/terms', function () {
    return view('terms');
});

Route::get('/privacy', function () {
    return view('privacy');
});

Route::get('/contact', function () {
    return view('contact');
});

Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified',
])->group(function () {
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');

    Route::get('/upload', [App\Http\Controllers\UploadController::class, 'index'])->name('upload');
    Route::get('/upload/get-type', [App\Http\Controllers\UploadController::class, 'getDocumentType'])->name('upload.get-type');
    Route::post('/upload/process', [App\Http\Controllers\UploadController::class, 'process'])->name('upload.process');
    Route::get('/documents', App\Livewire\DocumentsList::class)->name('documents');
    Route::get('/document/{document}', function (App\Models\Document $document) {
        return view('document-view', compact('document'));
    })->name('document.view');
    Route::get('/document/{document}/download/csv', [App\Http\Controllers\DocumentController::class, 'downloadCsv'])->name('document.download.csv');
    Route::get('/usage', [App\Http\Controllers\UsageController::class, 'index'])->name('usage');
    Route::get('/settings', [App\Http\Controllers\SettingsController::class, 'index'])->name('settings');
});
