<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class Document extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'team_id',
        'user_id',
        'document_name',
        'document_type',
        'request_type',
        'status',
        'extracted_data',
        'credit_used',
        'processed_at',
    ];

    protected $casts = [
        'extracted_data' => 'array',
        'processed_at' => 'datetime',
        'credit_used' => 'integer',
        'team_id' => 'integer',
        'user_id' => 'integer',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($document) {
            if (empty($document->uuid)) {
                $document->uuid = (string) Str::uuid();
            }
        });
    }

    /**
     * Get the document type details
     */
    public function documentType()
    {
        return $this->belongsTo(DocumentType::class, 'document_type', 'type');
    }

    /**
     * Get the user who created this document
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the team this document belongs to
     */
    public function team()
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * Scope to get documents for a specific user
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get documents for a specific team
     */
    public function scopeForTeam($query, $teamId)
    {
        return $query->where('team_id', $teamId);
    }

    /**
     * Scope to get documents by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get documents by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('document_type', $type);
    }

    /**
     * Scope to get documents within date range
     */
    public function scopeWithinDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Check if document processing is complete
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if document processing failed
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Check if document is still processing
     */
    public function isProcessing(): bool
    {
        return in_array($this->status, ['processing', 'pending']);
    }

    /**
     * Mark document as completed
     */
    public function markAsCompleted(array $extractedData, int $creditUsed): void
    {
        $this->update([
            'status' => 'completed',
            'extracted_data' => $extractedData,
            'credit_used' => $creditUsed,
            'processed_at' => now(),
        ]);
    }

    /**
     * Mark document as failed
     */
    public function markAsFailed(string $errorMessage): void
    {
        $this->update([
            'status' => 'failed',
            'extracted_data' => ['error' => $errorMessage],
            'processed_at' => now(),
        ]);
    }

    /**
     * Get route key name for URL binding
     */
    public function getRouteKeyName()
    {
        return 'uuid';
    }
}
