<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class DocumentType extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'description',
        'credit_cost',
        'file_count',
        'allowed_extensions',
        'max_file_size_mb',
        'is_active',
        'display_order',
    ];

    protected $casts = [
        'allowed_extensions' => 'array',
        'is_active' => 'boolean',
        'credit_cost' => 'integer',
        'file_count' => 'integer',
        'max_file_size_mb' => 'integer',
        'display_order' => 'integer',
    ];

    /**
     * Scope to get only active document types
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get document types ordered by display_order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('display_order', 'asc')->orderBy('name', 'asc');
    }

    /**
     * Scope to get enabled document types (display_order <= 100)
     */
    public function scopeEnabled($query)
    {
        return $query->where('display_order', '<=', 100);
    }

    /**
     * Check if document type is enabled for upload
     */
    public function isEnabled(): bool
    {
        return $this->is_active && $this->display_order <= 100;
    }

    /**
     * Get document type by type string
     */
    public static function findByType(string $type): ?self
    {
        return static::where('type', $type)->where('is_active', true)->first();
    }

    /**
     * Check if file extension is allowed for this document type
     */
    public function isExtensionAllowed(string $extension): bool
    {
        return in_array(strtolower($extension), array_map('strtolower', $this->allowed_extensions));
    }

    /**
     * Check if file size is within limits for this document type
     */
    public function isFileSizeAllowed(int $fileSizeBytes): bool
    {
        $maxSizeBytes = $this->max_file_size_mb * 1024 * 1024;
        return $fileSizeBytes <= $maxSizeBytes;
    }
}
