<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use <PERSON><PERSON>\Jetstream\Events\TeamCreated;
use <PERSON><PERSON>\Jetstream\Events\TeamDeleted;
use <PERSON><PERSON>\Jetstream\Events\TeamUpdated;
use <PERSON><PERSON>\Jetstream\Team as JetstreamTeam;

class Team extends JetstreamTeam
{
    /** @use HasFactory<\Database\Factories\TeamFactory> */
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'personal_team',
        'current_credits',
    ];

    /**
     * The event map for the model.
     *
     * @var array<string, class-string>
     */
    protected $dispatchesEvents = [
        'created' => TeamCreated::class,
        'updated' => TeamUpdated::class,
        'deleted' => TeamDeleted::class,
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'personal_team' => 'boolean',
            'current_credits' => 'integer',
        ];
    }

    /**
     * Get the total credits used by this team
     */
    public function getCreditsUsedAttribute()
    {
        return $this->hasMany(Document::class, 'team_id')->sum('credit_used');
    }

    /**
     * Get the remaining credits for this team
     */
    public function getRemainingCreditsAttribute()
    {
        return max(0, $this->current_credits - $this->credits_used);
    }

    /**
     * Get the documents for this team
     */
    public function documents()
    {
        return $this->hasMany(Document::class, 'team_id');
    }
}
