<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Document;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Response;

class DocumentController extends Controller
{
    /**
     * Download document data as CSV
     */
    public function downloadCsv(Document $document)
    {
        // Check if user has access to this document
        if (!$this->userCanAccessDocument($document)) {
            abort(403, 'You do not have permission to access this document.');
        }

        if (!$document->isCompleted()) {
            abort(404, 'Document processing is not complete or failed.');
        }

        $extractedData = $document->extracted_data;
        if (!$extractedData || !is_array($extractedData)) {
            abort(404, 'No data available for download.');
        }

        // Extract the actual data fields (skip 'type' and get 'data' content)
        $actualData = $extractedData['data'] ?? $extractedData;
        if (!is_array($actualData)) {
            abort(404, 'Invalid data format for download.');
        }

        // Prepare CSV data with proper structure
        $csvData = [];

        // Create header row with document info and extracted fields
        $headers = ['Document Name', 'Document Type', 'Upload Date', 'Processing Date'];
        foreach (array_keys($actualData) as $fieldName) {
            $headers[] = ucfirst(str_replace('_', ' ', $fieldName));
        }
        $csvData[] = $headers;

        // Create data row
        $dataRow = [
            $document->document_name,
            $document->documentType ? $document->documentType->name : $document->document_type,
            $document->created_at->format('Y-m-d H:i:s'),
            $document->processed_at ? $document->processed_at->format('Y-m-d H:i:s') : 'N/A'
        ];

        foreach ($actualData as $value) {
            if (is_array($value)) {
                $dataRow[] = json_encode($value);
            } else {
                $dataRow[] = $value;
            }
        }
        $csvData[] = $dataRow;

        // Generate CSV content
        $csvContent = '';
        foreach ($csvData as $row) {
            $csvContent .= '"' . implode('","', array_map('str_replace', ['""', '"'], ['\"', ''], $row)) . '"' . "\n";
        }

        $filename = 'document_' . $document->uuid . '_' . date('Y-m-d_H-i-s') . '.csv';

        return Response::make($csvContent, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);
    }

    /**
     * Check if the current user can access the document
     */
    private function userCanAccessDocument(Document $document): bool
    {
        if (!Auth::check()) {
            return false;
        }

        $user = Auth::user();

        // Check if user owns the document or is part of the same team
        return $document->user_id === $user->id ||
               $document->team_id === $user->currentTeam->id;
    }
}
