<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\DocumentType;
use App\Models\Document;
use App\Services\OcrService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class UploadController extends Controller
{
    /**
     * Show the upload form
     */
    public function index()
    {
        $documentTypes = DocumentType::active()->ordered()->get();

        return view('upload', compact('documentTypes'));
    }

    /**
     * Get document type details via AJAX
     */
    public function getDocumentType(Request $request)
    {
        $type = $request->get('type');
        $documentType = DocumentType::findByType($type);

        if (!$documentType) {
            return response()->json(['error' => 'Document type not found'], 404);
        }

        return response()->json([
            'name' => $documentType->name,
            'description' => $documentType->description,
            'credit_cost' => $documentType->credit_cost,
            'file_count' => $documentType->file_count,
            'allowed_extensions' => $documentType->allowed_extensions,
            'max_file_size_mb' => $documentType->max_file_size_mb,
            'is_enabled' => $documentType->isEnabled()
        ]);
    }

    /**
     * Process the uploaded document
     */
    public function process(Request $request)
    {
        // Basic validation
        $validator = Validator::make($request->all(), [
            'document_type' => 'required|string',
            'file1' => 'required|file',
            'file2' => 'nullable|file'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $documentType = DocumentType::findByType($request->document_type);
        if (!$documentType || !$documentType->isEnabled()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid or disabled document type'
            ], 422);
        }

        // Check file count requirement
        if ($documentType->file_count > 1 && !$request->hasFile('file2')) {
            return response()->json([
                'success' => false,
                'message' => 'This document type requires 2 files'
            ], 422);
        }

        try {
            // Store files temporarily
            $file1 = $request->file('file1');
            $file2 = $request->file('file2');

            $file1Path = $file1->store('temp', 'local');
            $file2Path = $file2 ? $file2->store('temp', 'local') : null;

            // Create document record
            $document = Document::create([
                'team_id' => Auth::user()->currentTeam->id,
                'user_id' => Auth::id(),
                'document_name' => $file1->getClientOriginalName(),
                'document_type' => $request->document_type,
                'request_type' => 'user_panel',
                'status' => 'processing',
            ]);

            // Process OCR
            $ocrService = new OcrService();
            $file1FullPath = Storage::disk('local')->path($file1Path);
            $file2FullPath = $file2Path ? Storage::disk('local')->path($file2Path) : null;

            $result = $ocrService->processOcr(
                $request->document_type,
                $file1FullPath,
                $file2FullPath,
                Auth::id(),
                Auth::user()->currentTeam->id
            );

            // Update document with results
            if ($result['type'] === 'success') {
                $document->markAsCompleted($result['data'], $documentType->credit_cost);
            } else {
                $document->markAsFailed($result['reason'] ?? 'OCR processing failed');
            }

            // Clean up temporary files
            Storage::disk('local')->delete($file1Path);
            if ($file2Path) {
                Storage::disk('local')->delete($file2Path);
            }

            return response()->json([
                'success' => true,
                'message' => 'Document processed successfully',
                'document_uuid' => $document->uuid,
                'redirect_url' => route('document.view', $document->uuid)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing the document: ' . $e->getMessage()
            ], 500);
        }
    }
}
