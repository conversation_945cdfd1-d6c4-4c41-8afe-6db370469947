<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Document;
use App\Models\DocumentType;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class UsageController extends Controller
{
    /**
     * Show the usage analytics page
     */
    public function index(Request $request)
    {
        $teamId = Auth::user()->currentTeam->id;
        
        // Get date range from request or default to last 30 days
        $endDate = $request->get('end_date', Carbon::now()->format('Y-m-d'));
        $startDate = $request->get('start_date', Carbon::now()->subDays(30)->format('Y-m-d'));
        
        // Parse dates
        $startDateTime = Carbon::parse($startDate)->startOfDay();
        $endDateTime = Carbon::parse($endDate)->endOfDay();
        
        // Get usage by document type
        $documentTypeUsage = Document::where('team_id', $teamId)
            ->whereBetween('created_at', [$startDateTime, $endDateTime])
            ->select('document_type', DB::raw('count(*) as count'), DB::raw('sum(credit_used) as credits'))
            ->groupBy('document_type')
            ->orderBy('count', 'desc')
            ->get();
        
        // Get usage by team member
        $teamMemberUsage = Document::where('team_id', $teamId)
            ->whereBetween('created_at', [$startDateTime, $endDateTime])
            ->join('users', 'documents.user_id', '=', 'users.id')
            ->select('users.name', 'users.id', DB::raw('count(*) as count'), DB::raw('sum(credit_used) as credits'))
            ->groupBy('users.id', 'users.name')
            ->orderBy('count', 'desc')
            ->get();
        
        // Get daily usage for chart
        $dailyUsage = Document::where('team_id', $teamId)
            ->whereBetween('created_at', [$startDateTime, $endDateTime])
            ->select(DB::raw('DATE(created_at) as date'), DB::raw('count(*) as count'))
            ->groupBy(DB::raw('DATE(created_at)'))
            ->orderBy('date')
            ->get();
        
        // Get status breakdown
        $statusBreakdown = Document::where('team_id', $teamId)
            ->whereBetween('created_at', [$startDateTime, $endDateTime])
            ->select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->get();
        
        // Get total statistics for the period
        $totalDocuments = Document::where('team_id', $teamId)
            ->whereBetween('created_at', [$startDateTime, $endDateTime])
            ->count();
        
        $totalCredits = Document::where('team_id', $teamId)
            ->whereBetween('created_at', [$startDateTime, $endDateTime])
            ->sum('credit_used') ?? 0;
        
        $completedDocuments = Document::where('team_id', $teamId)
            ->whereBetween('created_at', [$startDateTime, $endDateTime])
            ->where('status', 'completed')
            ->count();
        
        $failedDocuments = Document::where('team_id', $teamId)
            ->whereBetween('created_at', [$startDateTime, $endDateTime])
            ->where('status', 'failed')
            ->count();
        
        // Get document types for reference
        $documentTypes = DocumentType::active()->get()->keyBy('type');
        
        return view('usage', compact(
            'documentTypeUsage',
            'teamMemberUsage',
            'dailyUsage',
            'statusBreakdown',
            'totalDocuments',
            'totalCredits',
            'completedDocuments',
            'failedDocuments',
            'startDate',
            'endDate',
            'documentTypes'
        ));
    }
}
