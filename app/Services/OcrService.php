<?php

namespace App\Services;

use App\Models\OcrApiLog;
use App\Models\DocumentType;

class OcrService
{
    private const API_BASE_URL = 'https://doc.ornsys.com';
    private const TIMEOUT = 60; // 60 seconds timeout

    /**
     * Process OCR for the given image(s) using the external OCR API
     *
     * This method sends image data to the external OCR service and returns structured results.
     * It handles file validation, base64 encoding, API communication, and error handling.
     *
     * @param string $type The document type to process. Supported values:
     *                     - 'id': for testing
     *                     - 'nic': Sri Lankan National Identity Card 
     *                     - 'license': Sri Lankan Driving License
     *                     - 'vehicle': Vehicle Registration Certificate
     *                     - 'deed': Property Land Deed Document
     *                     - 'invoice': Invoice
     *                     - 'receipt': Receipt
     *
     * @param string $imagePath Absolute path to the primary image file to process.
     *                          Supported formats: JPEG, PNG, GIF, BMP, WebP
     *                          Maximum file size: 10MB
     *                          File must exist and be readable
     *
     * @param string|null $imagePath2 Optional absolute path to a second image file.
     *                                Same requirements as primary image.
     *                                Useful for documents with front/back sides or multiple pages.
     *
     * @param int|null $userId Optional user ID for logging purposes (null for unauthenticated requests)
     * @param int|null $teamId Optional team ID for logging purposes (null if not applicable)
     *
     * @return array Standardized response array with one of the following structures:
     *
     *               Success Response:
     *               [
     *                   'type' => 'success',
     *                   'data' => [
     *                       // OCR extracted data varies by document type
     *                       'name' => 'John Doe',           // For ID cards
     *                       'nic' => '123456789V',          // For ID cards
     *                       'address' => '123 Main St',     // For various documents
     *                       'license_number' => '********', // For driving licenses
     *                       // ... other extracted fields
     *                   ]
     *               ]
     *
     *               Error Response:
     *               [
     *                   'type' => 'error',
     *                   'reason' => 'Descriptive error message'
     *               ]
     *
     * @throws \Exception When unexpected errors occur during processing
     *
     * @example Basic usage:
     *          $ocrService = new OcrService();
     *          $result = $ocrService->processOcr('id', '/path/to/id_card.jpg');
     *
     *          if ($result['type'] === 'success') {
     *              $extractedData = $result['data'];
     *              echo "Name: " . $extractedData['name'];
     *          } else {
     *              echo "Error: " . $result['reason'];
     *          }
     *
     * @example With second image (front/back of document):
     *          $result = $ocrService->processOcr('id', '/path/to/front.jpg', '/path/to/back.jpg');
     *
     * @example Error handling:
     *          $result = $ocrService->processOcr('invalid_type', '/nonexistent/file.jpg');
     *          // Returns: ['type' => 'error', 'reason' => 'Image file not found.']
     *
     * @since 1.0.0
     * @version 1.1.0 Added support for multiple document types and enhanced validation
     *
     * Possible Error Scenarios:
     * - Image file not found or not readable
     * - Image file exceeds 10MB size limit
     * - Invalid image format (not a valid image file)
     * - Network connectivity issues with OCR API
     * - OCR API timeout (after 60 seconds)
     * - OCR API returns HTTP error status
     * - OCR API returns invalid JSON response
     * - OCR API returns application-level error
     * - Insufficient credits for document type
     * - Invalid document type parameter
     */
    public function processOcr(string $type, string $imagePath, ?string $imagePath2 = null, ?int $userId = null, ?int $teamId = null): array
    {
        try {
            // Validate document type exists and is active
            $documentType = DocumentType::findByType($type);
            if (!$documentType) {
                $typeError = [
                    'type' => 'error',
                    'reason' => 'Invalid or inactive document type.'
                ];
                $this->logApiRequest($type, $imagePath, $imagePath2, $typeError, $userId, $teamId);
                return $typeError;
            }

            // Validate file count requirements
            $requiredFileCount = $documentType->file_count;
            $providedFileCount = $imagePath2 !== null ? 2 : 1;

            if ($providedFileCount < $requiredFileCount) {
                $fileCountError = [
                    'type' => 'error',
                    'reason' => "This document type requires {$requiredFileCount} file(s), but only {$providedFileCount} provided."
                ];
                $this->logApiRequest($type, $imagePath, $imagePath2, $fileCountError, $userId, $teamId);
                return $fileCountError;
            }

            // Validate primary image file
            $validationError = $this->validateImage($imagePath, $documentType);
            if ($validationError !== null) {
                $this->logApiRequest($type, $imagePath, $imagePath2, $validationError, $userId, $teamId);
                return $validationError;
            }

            // Validate second image file if provided
            if ($imagePath2 !== null) {
                $validationError2 = $this->validateImage($imagePath2, $documentType);
                if ($validationError2 !== null) {
                    $this->logApiRequest($type, $imagePath, $imagePath2, $validationError2, $userId, $teamId);
                    return $validationError2;
                }
            }

            // API URL
            $apiUrl = self::API_BASE_URL . '/apico.php';

            // Read the primary image file and encode it as base64
            $imageData = base64_encode(file_get_contents($imagePath));

            // Primary image name
            $imageName = basename($imagePath);

            // Prepare POST data
            $postData = [
                'type' => $type,
                'name' => $imageName,
                'image' => $imageData
            ];

            // Add second image if provided
            if ($imagePath2 !== null) {
                $imageData2 = base64_encode(file_get_contents($imagePath2));
                $imageName2 = basename($imagePath2);

                $postData['name2'] = $imageName2;
                $postData['image2'] = $imageData2;
            }

            // Initialize cURL
            $ch = curl_init($apiUrl);

            // Set cURL options
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);  // To get the response back
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));  // Send POST data
            curl_setopt($ch, CURLOPT_TIMEOUT, self::TIMEOUT);  // Set timeout
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);  // Connection timeout

            // Execute cURL request and get the response
            $response = curl_exec($ch);

            // Check for cURL errors
            if (curl_errno($ch)) {
                $error = curl_error($ch);
                curl_close($ch);

                $curlErrorResponse = [
                    'type' => 'error',
                    'reason' => 'cURL error: ' . $error
                ];

                $this->logApiRequest($type, $imagePath, $imagePath2, $curlErrorResponse, $userId, $teamId);

                return $curlErrorResponse;
            }

            // Get HTTP status code
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            // Check HTTP status
            if ($httpCode !== 200) {
                $httpErrorResponse = [
                    'type' => 'error',
                    'reason' => 'HTTP error: ' . $httpCode
                ];

                $this->logApiRequest($type, $imagePath, $imagePath2, $httpErrorResponse, $userId, $teamId);

                return $httpErrorResponse;
            }

            // Check if response is empty
            if (empty($response)) {
                $emptyErrorResponse = [
                    'type' => 'error',
                    'reason' => 'Empty response from API.'
                ];

                $this->logApiRequest($type, $imagePath, $imagePath2, $emptyErrorResponse, $userId, $teamId);

                return $emptyErrorResponse;
            }

            // Try to decode JSON response
            $decodedResponse = json_decode($response, true);

            // If JSON decode failed, return the raw response as error
            if (json_last_error() !== JSON_ERROR_NONE) {
                $jsonErrorResponse = [
                    'type' => 'error',
                    'reason' => 'Invalid JSON response: ' . $response
                ];

                $this->logApiRequest($type, $imagePath, $imagePath2, $jsonErrorResponse, $userId, $teamId);

                return $jsonErrorResponse;
            }

            // Check if the API returned an error
            if (isset($decodedResponse['error']) || isset($decodedResponse['type']) && $decodedResponse['type'] === 'error') {
                $errorResponse = [
                    'type' => 'error',
                    'reason' => $decodedResponse['reason'] ?? $decodedResponse['error'] ?? 'Unknown API error.'
                ];

                // Log the API request and response
                $this->logApiRequest($type, $imagePath, $imagePath2, $errorResponse, $userId, $teamId);

                return $errorResponse;
            }

            // Prepare success response
            $successResponse = [
                'type' => 'success',
                'data' => $decodedResponse
            ];

            // Log the API request and response
            $this->logApiRequest($type, $imagePath, $imagePath2, $successResponse, $userId, $teamId);

            return $successResponse;

        } catch (\Exception $e) {
            $exceptionResponse = [
                'type' => 'error',
                'reason' => 'Exception: ' . $e->getMessage()
            ];

            $this->logApiRequest($type, $imagePath, $imagePath2, $exceptionResponse, $userId, $teamId);

            return $exceptionResponse;
        }
    }

    /**
     * Test the OCR service with the logo image
     *
     * @return array The OCR result
     */
    public function testWithLogo(): array
    {
        $logoPath = public_path('logo.png');
        return $this->processOcr('id', $logoPath);
    }

    /**
     * Validate image file before processing
     *
     * @param string $imagePath
     * @param DocumentType $documentType
     * @return array|null Returns error array if validation fails, null if valid
     */
    private function validateImage(string $imagePath, DocumentType $documentType): ?array
    {
        if (!file_exists($imagePath)) {
            return [
                'type' => 'error',
                'reason' => 'Image file not found.'
            ];
        }

        // Check file size using document type limits
        $fileSize = filesize($imagePath);
        if (!$documentType->isFileSizeAllowed($fileSize)) {
            return [
                'type' => 'error',
                'reason' => "Image file too large. Maximum size is {$documentType->max_file_size_mb}MB."
            ];
        }

        // Check file extension
        $pathInfo = pathinfo($imagePath);
        $extension = $pathInfo['extension'] ?? '';

        if (!$documentType->isExtensionAllowed($extension)) {
            $allowedExtensions = implode(', ', $documentType->allowed_extensions);
            return [
                'type' => 'error',
                'reason' => "Invalid file format. Allowed formats: {$allowedExtensions}"
            ];
        }

        // Check if it's a valid image (for image files)
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
        if (in_array(strtolower($extension), $imageExtensions)) {
            $imageInfo = getimagesize($imagePath);
            if ($imageInfo === false) {
                return [
                    'type' => 'error',
                    'reason' => 'Invalid image file format.'
                ];
            }
        }

        return null;
    }

    /**
     * Log OCR API request and response
     *
     * @param string $type Document type
     * @param string $imagePath Primary image path
     * @param string|null $imagePath2 Secondary image path
     * @param array $response API response
     * @param int|null $userId User ID (null for unauthenticated requests)
     * @param int|null $teamId Team ID (null if not applicable)
     */
    private function logApiRequest(
        string $type,
        string $imagePath,
        ?string $imagePath2,
        array $response,
        ?int $userId = null,
        ?int $teamId = null
    ): void {
        try {
            // Prepare request data (without base64 image data)
            $requestData = [
                'image1_name' => basename($imagePath),
            ];

            if ($imagePath2 !== null) {
                $requestData['image2_name'] = basename($imagePath2);
            }

            // Remove base64 image data from response to save space
            $cleanResponse = $response;
            if (isset($cleanResponse['data']) && is_array($cleanResponse['data'])) {
                // Remove any base64 image data that might be in the response
                unset($cleanResponse['data']['image']);
                unset($cleanResponse['data']['image1']);
                unset($cleanResponse['data']['image2']);
            }

            // Create log entry
            OcrApiLog::create([
                'user_id' => $userId,
                'team_id' => $teamId,
                'document_type' => $type,
                'api_request' => $requestData,
                'api_response' => $cleanResponse,
                'status' => $response['type'] === 'success' ? 'success' : 'error',
            ]);
        } catch (\Exception $e) {
            // Log the logging error but don't fail the main operation
            error_log('Failed to log OCR API request: ' . $e->getMessage());
        }
    }
}
