<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Document;
use App\Models\DocumentType;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class DocumentsList extends Component
{
    use WithPagination;

    public $documentTypeFilter = '';
    public $statusFilter = '';
    public $startDate = '';
    public $endDate = '';
    public $perPage = 15;
    public $showFilters = true;

    protected $queryString = [
        'documentTypeFilter' => ['except' => ''],
        'statusFilter' => ['except' => ''],
        'startDate' => ['except' => ''],
        'endDate' => ['except' => ''],
    ];

    public function mount()
    {
        // Set default date range to last 7 days
        $this->endDate = Carbon::now()->format('Y-m-d');
        $this->startDate = Carbon::now()->subDays(7)->format('Y-m-d');
    }

    public function applyFilters()
    {
        $this->resetPage();
    }

    public function clearFilters()
    {
        $this->documentTypeFilter = '';
        $this->statusFilter = '';
        $this->startDate = Carbon::now()->subDays(7)->format('Y-m-d');
        $this->endDate = Carbon::now()->format('Y-m-d');
        $this->resetPage();
    }

    public function toggleFilters()
    {
        $this->showFilters = !$this->showFilters;
    }

    public function render()
    {
        $query = Document::query()
            ->where('team_id', Auth::user()->currentTeam->id)
            ->with(['documentType']);

        // Apply filters
        if ($this->documentTypeFilter) {
            $query->where('document_type', $this->documentTypeFilter);
        }

        if ($this->statusFilter) {
            $query->where('status', $this->statusFilter);
        }

        if ($this->startDate && $this->endDate) {
            $query->whereBetween('created_at', [
                Carbon::parse($this->startDate)->startOfDay(),
                Carbon::parse($this->endDate)->endOfDay(),
            ]);
        }

        $documents = $query->orderBy('created_at', 'desc')
            ->paginate($this->perPage);

        $documentTypes = DocumentType::active()->ordered()->get();

        $statusOptions = [
            'pending' => 'Pending',
            'processing' => 'Processing',
            'completed' => 'Completed',
            'failed' => 'Failed',
        ];

        return view('livewire.documents-list', [
            'documents' => $documents,
            'documentTypes' => $documentTypes,
            'statusOptions' => $statusOptions,
        ])->layout('layouts.app');
    }
}
