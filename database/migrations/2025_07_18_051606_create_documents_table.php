<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('documents', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique(); // Unique identifier for public URLs
            $table->unsignedBigInteger('team_id')->nullable(); // Allow 0 for non-login users
            $table->unsignedBigInteger('user_id')->nullable(); // Allow 0 for non-login users
            $table->string('document_name'); // Original filename of primary image
            $table->string('document_type'); // References document_types.type
            $table->enum('request_type', ['user_panel', 'api', 'bulk'])->default('user_panel');
            $table->enum('status', ['processing', 'completed', 'failed', 'pending'])->default('pending');
            $table->json('extracted_data')->nullable(); // OCR results and structured data
            $table->integer('credit_used')->default(0); // Actual credits consumed
            $table->timestamp('processed_at')->nullable(); // When OCR completed
            $table->timestamps();

            // Add indexes for efficient filtering
            $table->index(['user_id', 'created_at']);
            $table->index(['team_id', 'created_at']);
            $table->index(['document_type', 'created_at']);
            $table->index(['status', 'created_at']);
            $table->index(['uuid']); // For fast lookups
            $table->index(['request_type', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('documents');
    }
};
