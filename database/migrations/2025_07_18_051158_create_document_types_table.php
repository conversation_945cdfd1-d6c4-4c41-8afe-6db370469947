<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('document_types', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Display name like "National ID Card"
            $table->string('type')->unique(); // API type like "id", "license", etc.
            $table->text('description'); // User-friendly description
            $table->integer('credit_cost')->default(1); // Credits required for processing
            $table->integer('file_count')->default(1); // Number of files required (1 for most, 2 for NIC)
            $table->json('allowed_extensions'); // e.g., ["pdf", "jpg", "png", "gif"]
            $table->integer('max_file_size_mb')->default(5); // Default 5MB
            $table->boolean('is_active')->default(true); // To enable/disable document types
            $table->timestamps();

            // Add indexes for better query performance
            $table->index(['type', 'is_active']);
            $table->index(['is_active', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('document_types');
    }
};
