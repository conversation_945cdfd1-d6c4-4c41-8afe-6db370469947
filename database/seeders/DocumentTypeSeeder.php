<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\DocumentType;

class DocumentTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $documentTypes = [
            [
                'name' => 'Test Document',
                'type' => 'id',
                'description' => 'For testing purposes only',
                'credit_cost' => 0,
                'file_count' => 1,
                'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
                'max_file_size_mb' => 10,
                'is_active' => true,
                'display_order' => 1,
            ],
            [
                'name' => 'National Identity Card',
                'type' => 'nic',
                'description' => 'Sri Lankan National Identity Card - Extract personal information including name, NIC number, address, and date of birth',
                'credit_cost' => 2,
                'file_count' => 2,
                'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
                'max_file_size_mb' => 5,
                'is_active' => true,
                'display_order' => 10,
            ],
            [
                'name' => 'Driving License',
                'type' => 'license',
                'description' => 'Sri Lankan Driving License - Extract license details including license number, name, address, and vehicle categories',
                'credit_cost' => 1,
                'file_count' => 1,
                'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
                'max_file_size_mb' => 5,
                'is_active' => true,
                'display_order' => 20,
            ],
            [
                'name' => 'Vehicle Registration Certificate',
                'type' => 'vehicle',
                'description' => 'Vehicle Registration Certificate - Extract vehicle details including registration number, owner information, and vehicle specifications',
                'credit_cost' => 2,
                'file_count' => 1,
                'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
                'max_file_size_mb' => 5,
                'is_active' => true,
                'display_order' => 30,
            ],
            [
                'name' => 'Invoice',
                'type' => 'invoice',
                'description' => 'Business Invoice - Extract invoice details including invoice number, amounts, dates, and vendor information',
                'credit_cost' => 1,
                'file_count' => 1,
                'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'pdf'],
                'max_file_size_mb' => 5,
                'is_active' => true,
                'display_order' => 40,
            ],
            [
                'name' => 'Receipt',
                'type' => 'receipt',
                'description' => 'Payment Receipt - Extract receipt details including amount, date, merchant information, and transaction details',
                'credit_cost' => 1,
                'file_count' => 1,
                'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'pdf'],
                'max_file_size_mb' => 5,
                'is_active' => true,
                'display_order' => 50,
            ],
            [
                'name' => 'Property Land Deed',
                'type' => 'deed',
                'description' => 'Property Land Deed Document - Extract property details including deed number, owner information, and property description',
                'credit_cost' => 5,
                'file_count' => 1,
                'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'pdf'],
                'max_file_size_mb' => 10,
                'is_active' => true,
                'display_order' => 60,
            ],
            // Future development placeholders (disabled for upload)
            [
                'name' => 'Passport (Coming Soon)',
                'type' => 'passport',
                'description' => 'Sri Lankan Passport - Extract passport details (Feature in development)',
                'credit_cost' => 3,
                'file_count' => 1,
                'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
                'max_file_size_mb' => 5,
                'is_active' => true,
                'display_order' => 110,
            ],
            [
                'name' => 'Bank Statement (Coming Soon)',
                'type' => 'bank_statement',
                'description' => 'Bank Statement - Extract transaction details (Feature in development)',
                'credit_cost' => 2,
                'file_count' => 1,
                'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'pdf'],
                'max_file_size_mb' => 10,
                'is_active' => true,
                'display_order' => 120,
            ],
        ];

        foreach ($documentTypes as $documentType) {
            DocumentType::updateOrCreate(
                ['type' => $documentType['type']],
                $documentType
            );
        }
    }
}
