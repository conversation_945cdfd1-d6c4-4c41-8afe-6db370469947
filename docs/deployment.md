# SmartOCR.lk - Deployment Guide

This guide covers the essential steps for deploying the SmartOCR.lk Laravel application.

## Prerequisites

- PHP 8.2 or higher
- Composer 2.x
- Node.js 18+ and npm
- MySQL 8.0+
- Web server (Apache/Nginx)

## Development Setup

### 1. Clone and Install Dependencies

```bash
# Clone the repository
git clone <repository-url> smartocr
cd smartocr

# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install
```

### 2. Environment Configuration

```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate
```

### 3. Database Configuration

Update your `.env` file with database credentials:

```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=smartocr2
DB_USERNAME=root
DB_PASSWORD=
```

Create the database:
```bash
mysql -u root -e "CREATE DATABASE smartocr2;"
```

### 4. Run Migrations

```bash
# Run database migrations
php artisan migrate
```

### 5. Build Frontend Assets

```bash
# Build assets for production
npm run build

# Or for development with hot reload
npm run dev
```

### 6. Start Development Server

```bash
# Start Laravel development server
php artisan serve
```

The application will be available at `http://127.0.0.1:8000`

## Production Deployment

### 1. Server Setup

Ensure your server has:
- PHP 8.2+ with required extensions
- MySQL 8.0+
- Apache/Nginx configured
- SSL certificate (recommended)

### 2. Application Deployment

```bash
# Upload files to server
# Install dependencies (production only)
composer install --optimize-autoloader --no-dev

# Set proper permissions
chmod -R 755 storage bootstrap/cache
chown -R www-data:www-data storage bootstrap/cache

# Generate application key
php artisan key:generate

# Run migrations
php artisan migrate --force

# Build assets
npm run build

# Cache configuration
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### 3. Web Server Configuration

#### Apache (.htaccess)
Ensure the document root points to the `public` directory.

#### Nginx
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/smartocr/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
```

## Features Included

- **Authentication**: User registration, login, password reset
- **Teams**: Multi-tenant team functionality
- **API Support**: Laravel Sanctum for API authentication
- **Two-Factor Authentication**: Built-in 2FA support
- **Profile Management**: User profile and password management

## Important Notes

- Ensure proper file permissions for `storage` and `bootstrap/cache` directories
- Configure your web server to point to the `public` directory
- Set up SSL certificates for production environments
- Configure proper backup strategies for your database
- Monitor application logs in `storage/logs`

## Troubleshooting

### Common Issues

1. **Permission Errors**: Ensure web server has write access to storage directories
2. **Database Connection**: Verify database credentials and server connectivity
3. **Asset Loading**: Run `npm run build` if CSS/JS assets are missing
4. **Cache Issues**: Clear caches with `php artisan cache:clear`

### Useful Commands

```bash
# Clear all caches
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Check application status
php artisan about

# Run queue workers (if using queues)
php artisan queue:work

# Schedule tasks (add to crontab)
* * * * * cd /path/to/smartocr && php artisan schedule:run >> /dev/null 2>&1
```

For additional support, refer to the [Laravel Documentation](https://laravel.com/docs) and [Jetstream Documentation](https://jetstream.laravel.com).
